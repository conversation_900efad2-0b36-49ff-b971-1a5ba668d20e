Observação deste documento:
Este documento serve para a Definição de Operações que devem aparecer
em cada Resource, incluindo a criação de novos campos para cada Operation

 ██████╗ ██████╗ ███████╗██████╗  █████╗ ████████╗██╗ ██████╗ ███╗   ██╗
██╔═══██╗██╔══██╗██╔════╝██╔══██╗██╔══██╗╚══██╔══╝██║██╔═══██╗████╗  ██║
██║   ██║██████╔╝█████╗  ██████╔╝███████║   ██║   ██║██║   ██║██╔██╗ ██║
██║   ██║██╔═══╝ ██╔══╝  ██╔══██╗██╔══██║   ██║   ██║██║   ██║██║╚██╗██║
╚██████╔╝██║     ███████╗██║  ██║██║  ██║   ██║   ██║╚██████╔╝██║ ╚████║
 ╚═════╝ ╚═╝     ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝ ╚═════╝ ╚═╝  ╚═══╝

Operation = Funções/Opções de cada Resource (Instancia, Mensagens, Integração, Conversa, Perfil e Grupo)

"O que cada Resource vai apresentar ao ser selecionado"

Exemplo:
Instancias:
  • Criar instncia;
  • Criar instancia com proxy;
  • Conectar instancia;

Cada Resource adicionada tera um desse para listar as opções/funções de cada Resource




	███████╗██╗     ███████╗███╗   ███╗███████╗███╗   ██╗████████╗███████╗
	██╔════╝██║     ██╔════╝████╗ ████║██╔════╝████╗  ██║╚══██╔══╝██╔════╝
	█████╗  ██║     █████╗  ██╔████╔██║█████╗  ██╔██╗ ██║   ██║   ███████╗
	██╔══╝  ██║     ██╔══╝  ██║╚██╔╝██║██╔══╝  ██║╚██╗██║   ██║   ╚════██║
	███████╗███████╗███████╗██║ ╚═╝ ██║███████╗██║ ╚████║   ██║   ███████║
	╚══════╝╚══════╝╚══════╝╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚══════╝

Elements = Os campos que aparecem em cada Operation

"Cada Operation (ex: Enviar mensagem de Texto) tem campos que precisam ser preenchido para fazer uma requisição"

Exemplo:
Enviar Mensagem de Texto:
  • Instancia que vai enviar;
  • RemoteJid do destinatario;
  • Mensagem que vai ser enviada;

