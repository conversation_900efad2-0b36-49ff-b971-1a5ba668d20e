{"name": "n8n-nodes-evolution-api", "version": "1.0.4", "description": "A Evolution API é um hub de canais com foco no WhatsApp", "keywords": ["n8n-community-node-package"], "license": "MIT", "homepage": "", "author": {"name": "OrionDesign", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/oriondesign2015/n8n-nodes-evolution-api.git"}, "engines": {"node": ">=18.10", "pnpm": ">=9.1"}, "packageManager": "pnpm@9.1.4", "main": "index.js", "scripts": {"preinstall": "npx only-allow pnpm", "build": "tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint nodes credentials package.json", "lintfix": "eslint nodes credentials package.json --fix", "prepublishOnly": "pnpm build && pnpm lint -c .eslintrc.prepublish.js nodes credentials package.json"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/EvolutionApi.credentials.js"], "nodes": ["dist/nodes/EvolutionApi/EvolutionApi.node.js"]}, "devDependencies": {"@types/node": "^22.13.10", "@typescript-eslint/parser": "^8.27.0", "eslint": "^8.57.1", "eslint-plugin-n8n-nodes-base": "^1.16.3", "gulp": "^5.0.0", "n8n-workflow": "^1.70.0", "prettier": "^3.5.3", "typescript": "^5.8.2"}, "peerDependencies": {"n8n-workflow": "*"}, "dependencies": {"axios": "^1.8.4"}, "resolutions": {"axios": "^1.8.4"}}