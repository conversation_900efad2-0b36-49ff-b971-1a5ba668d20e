![image](https://github.com/user-attachments/assets/813b7b34-377c-42e8-9f1a-12e27e682c7f)

<p align="center"><br>
This Community Node is a 100% free solution, created with the purpose of simplifying and helping the entire community to integrate and make the most of the main features offered by <b>Evolution API v2.2+</b> in their N8N projects. <b>Developed by OrionDesign.</b>
</p>
<br>
	
<div align="center">
  <img src="https://img.shields.io/badge/dynamic/json?url=https%3A%2F%2Fapi.npmjs.org%2Fdownloads%2Fpoint%2Flast-year%2Fn8n-nodes-evolution-api&query=downloads&style=for-the-badge&label=Total%20Downloads&labelColor=%230d1117&color=%23359514&cacheSeconds=30&link=https%3A%2F%2Fwww.npmjs.com%2Fpackage%2Fn8n-nodes-evolution-api" alt="Dynamic JSON Badge">
</div>
<br>
</p>
<p align="center">
  <a href="https://oriondesign.art.br/whatsapp1"><img src="https://github.com/user-attachments/assets/5a469114-2054-4f01-85b2-51a282518658" alt="SetupOrion" ></a>     
  <a href="https://oriondesign.art.br/whatsapp2"><img src="https://github.com/user-attachments/assets/3e3580a9-ae8e-4209-84fc-cfc1c03a8f12" alt="SetupOrion" ></a>     
  <a href="https://oriondesign.art.br/whatsapp3"><img src="https://github.com/user-attachments/assets/91aa7733-c09c-474f-9483-54cb678213d2" alt="SetupOrion" ></a>
</p>


<h1></h1>

<h3>⚙️ Requirements</h3>

To use our **Community Node**, you need to meet the following requirements:  
- **N8N** version **1.54.4** or higher  
- **Evolution API** version **2.2.0** or higher  

<h1></h1>

<h3>📌 Available Features</h3>

<h3>Instance</h3>
🖥️ This feature provides complete access to the main functionalities related to Evolution API instances. It allows you to perform essential operations such as creating new instances, connecting to them, obtaining detailed information, customizing behaviors, monitoring presence, restarting, and even deleting instances in a practical and efficient way.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Create Instance</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Generate QR Code</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Fetch Instance</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Set Behavior</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Set Presence</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Set Proxy</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Fetch Proxy</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Disconnect WhatsApp</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Delete Instance</b></summary>
	</details>
</details>

<h3> Message</h3>
✉️ This feature concentrates all functionalities related to sending and managing messages through the Evolution API. With it, you can send various types of content such as texts, images, videos, audios, documents, contacts, interactive lists, buttons, and even PIX messages. Each message type has advanced options like delay, mentions, replies, and special formatting. The feature was developed to provide a complete communication experience, allowing you to explore WhatsApp's native features to the fullest in a simple and efficient way.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Send Text</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Image</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Video</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Audio</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Document</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Poll</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Contact</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send List</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Button</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send PIX</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Status</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> React to Message</b></summary>
	</details>
</details>

<h3>Group</h3>
👥 With this feature, you have a complete set of functionalities for managing WhatsApp groups through the Evolution API. It covers everything from group creation and administration to participant management, permission configuration, invitation links, and temporary messages. Everything was designed to offer efficient and simplified control in group administration.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Create Group</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Update Group Image</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Update Group Name</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Update Group Description</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Update Group Settings</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Update Members</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Fetch Group Invite Link</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Revoke Group Invite Link</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Group Invite Link</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Find Participants</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Temporary Messages</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Leave Group</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Join Group</b></summary>
	</details>
</details>

<h3>Chat</h3>
💬 This feature provides a comprehensive set of tools for managing conversations and interactions using the Evolution API. With it, you can verify numbers, send and manage messages, manipulate media files, control read status, manage contacts, and monitor presence. All operations are developed to provide complete and efficient control of communications, facilitating the management of both individual and group conversations.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Verify Number</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Read Message</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Manage Archive</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Mark as Unread</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Delete Message</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Fetch Profile Picture</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Get Media in Base64</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Edit Message</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Send Presence</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Block Contact</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Fetch Contacts</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Search Messages</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Search Status</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Search Chats</b></summary>
	</details>
</details>

<h3>Event</h3>
⚡ This feature offers advanced mechanisms for integration and real-time monitoring of Evolution API activities. It allows you to configure and manage Webhooks and RabbitMQ, enabling the tracking of events such as received messages, group changes, connection status, and much more. These functionalities were designed to ensure agile and automated communication between your application and the API, promoting immediate responses to different WhatsApp events.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Webhook</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> RabbitMQ</b></summary>
	</details>
</details>

<h3>Integration</h3>
🔗 This feature provides a wide range of connectors to integrate the Evolution API with various external platforms and services. Among the possibilities are connections with Chatwoot for customer service, Evolution Bot for automations, Typebot for conversational flows, as well as integrations with Flowise and Dify for artificial intelligence solutions. These integrations expand the API's capabilities, allowing you to create robust and automated solutions for different business scenarios.
<br>
<details>
  <summary><b>Operations list</b></summary>
	<details>
  	<summary>   ✅ <b> Chatwoot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Evolution Bot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Typebot</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Dify</b></summary>
	</details>
	<details>
  	<summary>   ✅ <b> Flowise</b></summary>
	</details>
</details>

<h1></h1>

<h3>🤝 Contribution</h3>

Contribute to the growth of this project! You can help in various ways:  
- **Pull Requests**: Send improvements, fixes, or new features.  
- **Issues**: Report problems or suggest new ideas.  
- **Suggestions**: Share your opinions and feedback.  
- **Documentation**: Help improve or expand existing documentation.  

<h1></h1>

<h3>📌 Main contributors</h3>
<a align="center" href="https://github.com/oriondesign2015/n8n-nodes-evolution-api/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=oriondesign2015/n8n-nodes-evolution-api" />
</a>

<h1></h1>
<a href="https://star-history.com/#oriondesign2015/SetupOrion&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=oriondesign2015/SetupOrion&type=Date" />
 </picture>
</a>

<h1></h1>
<p align="center">
Developed with ❤️ by OrionDesign
</p>
